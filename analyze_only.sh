#!/bin/bash

# Script CHỈ ĐỌC và phân tích token từ file token.txt
# KHÔNG THAY ĐỔI file gốc token.txt

echo "🔍 Đang phân tích file token.txt (KHÔNG thay đổi file gốc)..."

# Tạo file tạm để lưu token (CHỈ để phân tích)
temp_file="temp_analysis.tmp"

# Xóa file tạm nếu tồn tại
rm -f "$temp_file"

echo "📋 Đang đọc và trích xuất token..."

# ĐỌC file token.txt và trích xuất token
while IFS= read -r line; do
    if [[ -n "$line" ]]; then
        # Tìm tất cả các mảng JSON trong dòng và trích xuất token
        echo "$line" | grep -oE '\["[^]]*"\]' | while read -r json_array; do
            # Trích xuất token từ mảng JSON
            echo "$json_array" | sed 's/\[//g; s/\]//g; s/"//g' | tr ',' '\n' | while read -r token; do
                # Loại bỏ khoảng trắng và lưu vào file tạm
                clean_token=$(echo "$token" | sed 's/^[[:space:]]*//; s/[[:space:]]*$//')
                if [[ -n "$clean_token" ]]; then
                    echo "$clean_token" >> "$temp_file"
                fi
            done
        done
    fi
done < token.txt

echo "📊 Đang phân tích dữ liệu..."

if [[ -f "$temp_file" ]]; then
    total_tokens=$(wc -l < "$temp_file")
    unique_tokens=$(sort "$temp_file" | uniq | wc -l)
    
    echo ""
    echo "📊 THỐNG KÊ TỔNG QUAN:"
    echo "======================"
    echo "📊 Tổng số token: $total_tokens"
    echo "📊 Số token unique: $unique_tokens"
    echo ""
    echo "🏆 TOP 10 TOKEN XUẤT HIỆN NHIỀU NHẤT:"
    echo "====================================="
    
    # Đếm và hiển thị top 10
    rank=1
    sort "$temp_file" | uniq -c | sort -nr | head -10 | while read -r count token; do
        if [[ -n "$token" && "$token" != " " ]]; then
            percentage=$(echo "scale=2; $count * 100 / $total_tokens" | bc -l 2>/dev/null || echo "0.00")
            printf "%2d. %-15s - %3d lần (%s%%)\n" "$rank" "$token" "$count" "$percentage"
            ((rank++))
        fi
    done
    
    # Lưu kết quả phân tích vào file mới
    {
        echo "PHÂN TÍCH TOP 10 TOKEN TỪ FILE token.txt"
        echo "========================================"
        echo ""
        echo "File gốc: token.txt (KHÔNG bị thay đổi)"
        echo "Thời gian phân tích: $(date)"
        echo ""
        echo "Tổng số token: $total_tokens"
        echo "Số token unique: $unique_tokens"
        echo ""
        echo "TOP 10 TOKEN:"
        echo "============="
        
        rank=1
        sort "$temp_file" | uniq -c | sort -nr | head -10 | while read -r count token; do
            if [[ -n "$token" && "$token" != " " ]]; then
                percentage=$(echo "scale=2; $count * 100 / $total_tokens" | bc -l 2>/dev/null || echo "0.00")
                printf "%2d. %-15s - %3d lần (%s%%)\n" "$rank" "$token" "$count" "$percentage"
                ((rank++))
            fi
        done
        
    } > analysis_result.txt
    
    echo ""
    echo "💾 Kết quả phân tích đã lưu vào: analysis_result.txt"
    echo "📁 File gốc token.txt KHÔNG bị thay đổi"
    
    # Xóa file tạm
    rm -f "$temp_file"
    
else
    echo "❌ Không thể đọc dữ liệu từ file token.txt"
fi

echo ""
echo "✅ Hoàn thành! File token.txt vẫn nguyên vẹn."
