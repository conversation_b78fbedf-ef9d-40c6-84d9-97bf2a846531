#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để phân tích và lọc ra top 10 token xuất hiện nhiều nhất từ file token.txt
"""

import json
import re
from collections import Counter

def parse_token_file(filename):
    """
    Đọc và phân tích file token.txt
    """
    all_tokens = []
    
    with open(filename, 'r', encoding='utf-8') as file:
        for line_num, line in enumerate(file, 1):
            line = line.strip()
            if not line:  # Bỏ qua dòng trống
                continue
                
            try:
                # Tìm tất cả các mảng JSON trong dòng
                json_arrays = re.findall(r'\[.*?\]', line)
                
                for json_str in json_arrays:
                    try:
                        tokens = json.loads(json_str)
                        if isinstance(tokens, list):
                            all_tokens.extend(tokens)
                    except json.JSONDecodeError:
                        print(f"Lỗi parse JSON tại dòng {line_num}: {json_str}")
                        continue
                        
            except Exception as e:
                print(f"Lỗi xử lý dòng {line_num}: {e}")
                continue
    
    return all_tokens

def get_top_tokens(tokens, top_n=10):
    """
    Lấy top N token xuất hiện nhiều nhất
    """
    # Đếm số lần xuất hiện của mỗi token
    token_counts = Counter(tokens)
    
    # Lấy top N token
    top_tokens = token_counts.most_common(top_n)
    
    return top_tokens

def main():
    print("🔍 Đang phân tích file token.txt...")
    
    # Đọc và phân tích file
    tokens = parse_token_file('token.txt')
    
    print(f"📊 Tổng số token tìm thấy: {len(tokens)}")
    print(f"📊 Số token unique: {len(set(tokens))}")
    
    # Lấy top 10 token
    top_10_tokens = get_top_tokens(tokens, 10)
    
    print("\n🏆 TOP 10 TOKEN XUẤT HIỆN NHIỀU NHẤT:")
    print("=" * 50)
    
    for rank, (token, count) in enumerate(top_10_tokens, 1):
        percentage = (count / len(tokens)) * 100
        print(f"{rank:2d}. {token:<15} - {count:3d} lần ({percentage:.1f}%)")
    
    print("\n" + "=" * 50)
    
    # Thống kê thêm
    print(f"\n📈 THỐNG KÊ THÊM:")
    print(f"   • Token xuất hiện nhiều nhất: {top_10_tokens[0][0]} ({top_10_tokens[0][1]} lần)")
    print(f"   • Tỷ lệ token top 1: {(top_10_tokens[0][1] / len(tokens)) * 100:.1f}%")
    
    # Xuất ra file kết quả
    with open('top_10_tokens.txt', 'w', encoding='utf-8') as f:
        f.write("TOP 10 TOKEN XUẤT HIỆN NHIỀU NHẤT\n")
        f.write("=" * 40 + "\n\n")
        for rank, (token, count) in enumerate(top_10_tokens, 1):
            percentage = (count / len(tokens)) * 100
            f.write(f"{rank:2d}. {token:<15} - {count:3d} lần ({percentage:.1f}%)\n")
    
    print(f"\n💾 Kết quả đã được lưu vào file: top_10_tokens.txt")

if __name__ == "__main__":
    main()
