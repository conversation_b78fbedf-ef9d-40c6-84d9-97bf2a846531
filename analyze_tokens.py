#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để làm sạch dữ liệu và phân tích token từ file token.txt
"""

import json
import re
from collections import Counter

def clean_and_parse_token_file(filename):
    """
    Làm sạch và phân tích file token.txt
    """
    all_tokens = []
    problematic_lines = []

    with open(filename, 'r', encoding='utf-8') as file:
        for line_num, line in enumerate(file, 1):
            line = line.strip()
            if not line:  # Bỏ qua dòng trống
                continue

            try:
                # Tìm tất cả các mảng JSON trong dòng
                json_arrays = re.findall(r'\[.*?\]', line)

                for json_str in json_arrays:
                    try:
                        tokens = json.loads(json_str)
                        if isinstance(tokens, list):
                            # Lọc bỏ token rỗng hoặc chỉ có khoảng trắng
                            clean_tokens = [token.strip() for token in tokens if token.strip()]
                            all_tokens.extend(clean_tokens)

                            # Ghi nhận dòng có quá nhiều token (có thể là lỗi)
                            if len(clean_tokens) > 50:
                                problematic_lines.append({
                                    'line': line_num,
                                    'token_count': len(clean_tokens),
                                    'preview': clean_tokens[:10]
                                })

                    except json.JSONDecodeError:
                        print(f"⚠️  Lỗi parse JSON tại dòng {line_num}")
                        continue

            except Exception as e:
                print(f"❌ Lỗi xử lý dòng {line_num}: {e}")
                continue

    return all_tokens, problematic_lines

def get_top_tokens(tokens, top_n=10):
    """
    Lấy top N token xuất hiện nhiều nhất
    """
    # Đếm số lần xuất hiện của mỗi token
    token_counts = Counter(tokens)
    
    # Lấy top N token
    top_tokens = token_counts.most_common(top_n)
    
    return top_tokens

def create_clean_dataset(tokens, problematic_lines):
    """
    Tạo dataset sạch và báo cáo vấn đề
    """
    print("\n🧹 PHÂN TÍCH CHẤT LƯỢNG DỮ LIỆU:")
    print("=" * 50)

    if problematic_lines:
        print(f"⚠️  Phát hiện {len(problematic_lines)} dòng có vấn đề:")
        for issue in problematic_lines:
            print(f"   • Dòng {issue['line']}: {issue['token_count']} tokens")
            print(f"     Preview: {', '.join(issue['preview'])}...")

    # Lọc token hợp lệ
    valid_tokens = []
    for token in tokens:
        # Loại bỏ token quá ngắn hoặc quá dài
        if 1 <= len(token) <= 20 and token.isalnum():
            valid_tokens.append(token.upper())  # Chuẩn hóa về chữ hoa

    print(f"\n📊 Tokens trước khi làm sạch: {len(tokens)}")
    print(f"📊 Tokens sau khi làm sạch: {len(valid_tokens)}")
    print(f"📊 Tokens bị loại bỏ: {len(tokens) - len(valid_tokens)}")

    return valid_tokens

def main():
    print("🔍 Đang phân tích và làm sạch file token.txt...")

    # Đọc và phân tích file
    tokens, problematic_lines = clean_and_parse_token_file('token.txt')

    # Làm sạch dữ liệu
    clean_tokens = create_clean_dataset(tokens, problematic_lines)

    print(f"\n📊 Tổng số token hợp lệ: {len(clean_tokens)}")
    print(f"📊 Số token unique: {len(set(clean_tokens))}")

    # Lấy top 10 token
    top_10_tokens = get_top_tokens(clean_tokens, 10)

    print("\n🏆 TOP 10 TOKEN XUẤT HIỆN NHIỀU NHẤT (SAU KHI LÀM SẠCH):")
    print("=" * 60)

    for rank, (token, count) in enumerate(top_10_tokens, 1):
        percentage = (count / len(clean_tokens)) * 100
        print(f"{rank:2d}. {token:<15} - {count:3d} lần ({percentage:.2f}%)")

    print("\n" + "=" * 60)

    # Thống kê thêm
    print(f"\n📈 THỐNG KÊ CHI TIẾT:")
    print(f"   • Token phổ biến nhất: {top_10_tokens[0][0]} ({top_10_tokens[0][1]} lần)")
    print(f"   • Tỷ lệ xuất hiện: {(top_10_tokens[0][1] / len(clean_tokens)) * 100:.2f}%")
    print(f"   • Độ phân tán: {len(set(clean_tokens))} token khác nhau")

    # Xuất ra file kết quả
    with open('top_10_tokens_clean.txt', 'w', encoding='utf-8') as f:
        f.write("TOP 10 TOKEN XUẤT HIỆN NHIỀU NHẤT (DỮ LIỆU ĐÃ LÀM SẠCH)\n")
        f.write("=" * 55 + "\n\n")
        f.write(f"Tổng số token phân tích: {len(clean_tokens)}\n")
        f.write(f"Số token unique: {len(set(clean_tokens))}\n\n")

        for rank, (token, count) in enumerate(top_10_tokens, 1):
            percentage = (count / len(clean_tokens)) * 100
            f.write(f"{rank:2d}. {token:<15} - {count:3d} lần ({percentage:.2f}%)\n")

    print(f"\n💾 Kết quả đã được lưu vào file: top_10_tokens_clean.txt")

if __name__ == "__main__":
    main()
