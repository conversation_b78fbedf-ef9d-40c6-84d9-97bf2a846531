#!/bin/bash

# Script cải tiến để làm sạch và phân tích token từ file token.txt

echo "🔍 Đang phân tích và làm sạch file token.txt..."

# Tạo các file tạm
temp_file="all_tokens.tmp"
clean_file="clean_tokens.tmp"
problem_lines="problem_lines.txt"

# Xóa các file tạm nếu tồn tại
rm -f "$temp_file" "$clean_file" "$problem_lines"

echo "📋 Đang trích xuất token từ file..."

line_num=0
total_lines=$(wc -l < token.txt)

# Đọc từng dòng và phân tích
while IFS= read -r line; do
    ((line_num++))
    
    if [[ -n "$line" ]]; then
        # Đếm số mảng JSON trong dòng
        array_count=$(echo "$line" | grep -o '\[' | wc -l)
        
        # Trích xuất tất cả token từ các mảng JSON
        echo "$line" | grep -oE '\["[^]]*"\]' | while read -r json_array; do
            # Đ<PERSON><PERSON> số token trong mảng này
            token_count=$(echo "$json_array" | grep -o '","' | wc -l)
            ((token_count++))  # Cộng thêm 1 vì số dấu phẩy ít hơn số token 1
            
            # Ghi nhận dòng có vấn đề (quá nhiều token)
            if [[ $token_count -gt 50 ]]; then
                echo "Dòng $line_num: $token_count tokens (có thể có vấn đề)" >> "$problem_lines"
            fi
            
            # Trích xuất token và làm sạch
            echo "$json_array" | sed 's/\[//g; s/\]//g; s/"//g' | tr ',' '\n' | while read -r token; do
                # Loại bỏ khoảng trắng đầu cuối
                clean_token=$(echo "$token" | sed 's/^[[:space:]]*//; s/[[:space:]]*$//')
                
                # Chỉ giữ token hợp lệ (1-20 ký tự, chỉ chữ và số)
                if [[ ${#clean_token} -ge 1 && ${#clean_token} -le 20 && "$clean_token" =~ ^[A-Za-z0-9]+$ ]]; then
                    # Chuyển về chữ hoa để chuẩn hóa
                    echo "${clean_token^^}" >> "$temp_file"
                fi
            done
        done
    fi
    
    # Hiển thị tiến trình
    if (( line_num % 10 == 0 )); then
        progress=$((line_num * 100 / total_lines))
        echo "   Đã xử lý: $line_num/$total_lines dòng ($progress%)"
    fi
done < token.txt

echo "🧹 Đang làm sạch và phân tích dữ liệu..."

if [[ -f "$temp_file" ]]; then
    # Thống kê trước khi làm sạch
    total_tokens=$(wc -l < "$temp_file")
    
    # Loại bỏ token trùng lặp liên tiếp và tạo file sạch
    sort "$temp_file" | uniq > "$clean_file"
    
    # Thống kê sau khi làm sạch
    clean_total=$(wc -l < "$clean_file")
    unique_tokens=$(wc -l < "$clean_file")
    
    echo ""
    echo "📊 KẾT QUẢ LÀM SẠCH DỮ LIỆU:"
    echo "================================"
    echo "📊 Token trước khi làm sạch: $total_tokens"
    echo "📊 Token sau khi làm sạch: $clean_total"
    echo "📊 Token unique: $unique_tokens"
    
    # Hiển thị các dòng có vấn đề nếu có
    if [[ -f "$problem_lines" && -s "$problem_lines" ]]; then
        echo ""
        echo "⚠️  CÁC DÒNG CÓ VẤN đề:"
        cat "$problem_lines"
    fi
    
    echo ""
    echo "🏆 TOP 10 TOKEN XUẤT HIỆN NHIỀU NHẤT (DỮ LIỆU ĐÃ LÀM SẠCH):"
    echo "============================================================"
    
    # Đếm lại từ file gốc để có số lần xuất hiện chính xác
    rank=1
    sort "$temp_file" | uniq -c | sort -nr | head -10 | while read -r count token; do
        if [[ -n "$token" && "$token" != " " ]]; then
            percentage=$(echo "scale=2; $count * 100 / $total_tokens" | bc -l 2>/dev/null || echo "0.00")
            printf "%2d. %-15s - %3d lần (%s%%)\n" "$rank" "$token" "$count" "$percentage"
            ((rank++))
        fi
    done
    
    # Lưu kết quả vào file
    {
        echo "TOP 10 TOKEN XUẤT HIỆN NHIỀU NHẤT (DỮ LIỆU ĐÃ LÀM SẠCH)"
        echo "========================================================"
        echo ""
        echo "Tổng số token phân tích: $total_tokens"
        echo "Số token unique: $unique_tokens"
        echo ""
        
        rank=1
        sort "$temp_file" | uniq -c | sort -nr | head -10 | while read -r count token; do
            if [[ -n "$token" && "$token" != " " ]]; then
                percentage=$(echo "scale=2; $count * 100 / $total_tokens" | bc -l 2>/dev/null || echo "0.00")
                printf "%2d. %-15s - %3d lần (%s%%)\n" "$rank" "$token" "$count" "$percentage"
                ((rank++))
            fi
        done
        
        echo ""
        echo "Ngày phân tích: $(date)"
        
    } > top_10_tokens_final.txt
    
    echo ""
    echo "💾 Kết quả đã được lưu vào file: top_10_tokens_final.txt"
    
    # Xóa các file tạm
    rm -f "$temp_file" "$clean_file"
    
else
    echo "❌ Không thể tạo file tạm để xử lý"
fi

echo ""
echo "✅ Hoàn thành phân tích!"
