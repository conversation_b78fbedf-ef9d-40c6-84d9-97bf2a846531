#!/bin/bash

# Script để đếm và lọc top 10 token từ file token.txt

echo "🔍 Đang phân tích file token.txt..."

# Tạo file tạm để lưu tất cả token
temp_file="all_tokens.tmp"

# Xóa file tạm nếu tồn tại
rm -f "$temp_file"

# Đọc từng dòng và trích xuất token từ các mảng JSON
while IFS= read -r line; do
    if [[ -n "$line" ]]; then
        # Tìm tất cả các mảng JSON trong dòng và trích xuất token
        echo "$line" | grep -oE '\["[^"]*"[^]]*\]' | while read -r json_array; do
            # Loại bỏ dấu ngoặc vuông và tách token
            tokens=$(echo "$json_array" | sed 's/\[//g; s/\]//g; s/"//g' | tr ',' '\n')
            echo "$tokens" >> "$temp_file"
        done
    fi
done < token.txt

# Đếm số lần xuất hiện của mỗi token và sắp xếp
echo "📊 Đang đếm và sắp xếp token..."

if [[ -f "$temp_file" ]]; then
    total_tokens=$(wc -l < "$temp_file")
    unique_tokens=$(sort "$temp_file" | uniq | wc -l)
    
    echo "📊 Tổng số token: $total_tokens"
    echo "📊 Số token unique: $unique_tokens"
    echo ""
    echo "🏆 TOP 10 TOKEN XUẤT HIỆN NHIỀU NHẤT:"
    echo "=================================================="
    
    # Đếm và sắp xếp top 10
    sort "$temp_file" | uniq -c | sort -nr | head -10 | while read -r count token; do
        if [[ -n "$token" && "$token" != " " ]]; then
            percentage=$(echo "scale=1; $count * 100 / $total_tokens" | bc -l 2>/dev/null || echo "0.0")
            printf "%2d. %-15s - %3d lần (%.1f%%)\n" "$((++rank))" "$token" "$count" "$percentage"
        fi
    done
    
    # Lưu kết quả vào file
    {
        echo "TOP 10 TOKEN XUẤT HIỆN NHIỀU NHẤT"
        echo "=================================="
        echo ""
        sort "$temp_file" | uniq -c | sort -nr | head -10 | while read -r count token; do
            if [[ -n "$token" && "$token" != " " ]]; then
                percentage=$(echo "scale=1; $count * 100 / $total_tokens" | bc -l 2>/dev/null || echo "0.0")
                printf "%-15s - %3d lần (%.1f%%)\n" "$token" "$count" "$percentage"
            fi
        done
    } > top_10_tokens_result.txt
    
    echo ""
    echo "💾 Kết quả đã được lưu vào file: top_10_tokens_result.txt"
    
    # Xóa file tạm
    rm -f "$temp_file"
else
    echo "❌ Không thể tạo file tạm để xử lý"
fi
