#!/bin/bash

# Script đơn giản CHỈ ĐỌC và phân tích token từ file token.txt
# ĐẢM BẢO KHÔNG THAY ĐỔI file gốc

echo "🔍 Phân tích file token.txt (file gốc KHÔNG thay đổi)..."

# Tạo file tạm
temp_tokens="tokens_temp.txt"
rm -f "$temp_tokens"

echo "📋 Đang trích xuất token..."

# Đọc file và trích xuất token một cách đơn giản
cat token.txt | grep -oE '"[A-Za-z0-9]+"' | sed 's/"//g' > "$temp_tokens"

if [[ -f "$temp_tokens" && -s "$temp_tokens" ]]; then
    total=$(wc -l < "$temp_tokens")
    unique=$(sort "$temp_tokens" | uniq | wc -l)
    
    echo "📊 Tổng token: $total"
    echo "📊 Token unique: $unique"
    echo ""
    echo "🏆 TOP 10 TOKEN:"
    echo "================"
    
    # Top 10
    sort "$temp_tokens" | uniq -c | sort -nr | head -10 | nl -w2 -s'. '
    
    # L<PERSON><PERSON> kết quả
    {
        echo "TOP 10 TOKEN TỪ token.txt"
        echo "========================"
        echo "File gốc: KHÔNG thay đổi"
        echo "Tổng: $total tokens"
        echo "Unique: $unique tokens"
        echo ""
        sort "$temp_tokens" | uniq -c | sort -nr | head -10 | nl -w2 -s'. '
        echo ""
        echo "Thời gian: $(date)"
    } > final_top10.txt
    
    echo ""
    echo "💾 Lưu kết quả: final_top10.txt"
    echo "📁 File token.txt vẫn nguyên vẹn!"
    
    # Xóa file tạm
    rm -f "$temp_tokens"
else
    echo "❌ Không đọc được dữ liệu"
fi
